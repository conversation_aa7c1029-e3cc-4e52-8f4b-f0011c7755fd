import { api } from './api'

export interface NotificationResponse {
  data: {
    notifications: Array<{
      id: number
      type: string
      title: string
      message: string
      data?: string
      task_id?: string
      status: 'read' | 'unread'
      created_at: string
      read_at?: string
    }>
    limit: number
    offset: number
    count: number
  }
}

export class NotificationService {
  static async getAllNotifications(limit = 20, offset = 0): Promise<NotificationResponse> {
    const response = await api.get<NotificationResponse>(`/v1/notifications?limit=${limit}&offset=${offset}`, {
      requiresAuth: true
    })
    
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to fetch notifications')
    }
    
    return response.data
  }

  static async getUnreadNotifications(limit = 20, offset = 0): Promise<NotificationResponse> {
    const response = await api.get<NotificationResponse>(`/v1/notifications/unread?limit=${limit}&offset=${offset}`, {
      requiresAuth: true
    })
    
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to fetch unread notifications')
    }
    
    return response.data
  }

  static async markAsRead(notificationId: number): Promise<void> {
    const response = await api.put(`/v1/notifications/${notificationId}/read`, undefined, {
      requiresAuth: true
    })
    
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to mark notification as read')
    }
  }

  static async markAllAsRead(): Promise<void> {
    const response = await api.put('/v1/notifications/read-all', undefined, {
      requiresAuth: true
    })
    
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to mark all notifications as read')
    }
  }

  static async deleteNotification(notificationId: number): Promise<void> {
    const response = await api.delete(`/v1/notifications/${notificationId}`, {
      requiresAuth: true
    })
    
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to delete notifications')
    }
  }

  static async clearAllNotifications(): Promise<void> {
    const response = await api.put('/v1/notifications', undefined, {
      requiresAuth: true
    })
    
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to clear all notifications')
    }
  }
}
