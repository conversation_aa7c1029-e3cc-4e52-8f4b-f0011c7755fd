import { ref, onMounted, onUnmounted } from 'vue'
import { usePersistentNotificationStore } from '../stores/persistentNotifications'
import { useUserStore } from '../stores/user'

export function useNotificationEvents() {
  const eventSource = ref<EventSource | null>(null)
  const isConnected = ref(false)
  const notificationStore = usePersistentNotificationStore()
  const userStore = useUserStore()

  const connect = () => {
    if (!userStore.isLoggedIn || eventSource.value) return

    const token = localStorage.getItem('token')
    if (!token) return

    try {
      // Create EventSource with authentication
      const url = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'}/api/v1/events`
      eventSource.value = new EventSource(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      } as any)

      eventSource.value.onopen = () => {
        isConnected.value = true
        console.log('SSE connection established')
      }

      eventSource.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleNotificationEvent(data)
        } catch (error) {
          console.error('Failed to parse SSE message:', error)
        }
      }

      eventSource.value.onerror = (error) => {
        console.error('SSE connection error:', error)
        isConnected.value = false
        // Attempt to reconnect after a delay
        setTimeout(() => {
          if (userStore.isLoggedIn) {
            connect()
          }
        }, 5000)
      }
    } catch (error) {
      console.error('Failed to establish SSE connection:', error)
    }
  }

  const handleNotificationEvent = (data: any) => {
    if (data.type === 'connected') {
      return // Skip connection messages
    }

    // Convert SSE message to notification format
    const notification = {
      id: Date.now(), // Generate temporary ID
      type: data.type || 'task_update',
      title: data.data?.status || 'Notification',
      message: data.data?.message || 'New notification',
      data: JSON.stringify(data.data || {}),
      task_id: data.taskId || '',
      status: 'unread' as const,
      created_at: new Date().toISOString(),
      read_at: undefined
    }

    // Add to store
    notificationStore.addNotification(notification)
  }

  const disconnect = () => {
    if (eventSource.value) {
      eventSource.value.close()
      eventSource.value = null
      isConnected.value = false
    }
  }

  onMounted(() => {
    if (userStore.isLoggedIn) {
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    isConnected,
    connect,
    disconnect
  }
}
